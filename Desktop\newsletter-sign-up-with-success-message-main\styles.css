/* Import Roboto font */
@font-face {
  font-family: 'Roboto';
  src: url('./assets/fonts/Roboto-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src: url('./assets/fonts/Roboto-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: hsl(234, 29%, 20%);
  background-color: hsl(235, 18%, 26%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

/* Container */
.container {
  width: 100%;
  max-width: 928px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 2rem);
}

/* Sign-up Card */
.signup-card {
  background-color: hsl(0, 0%, 100%);
  border-radius: 36px;
  padding: 24px;
  width: 100%;
  max-width: 928px;
  box-shadow: 0 15px 60px rgba(0, 0, 0, 0.25);
}

.content {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 64px;
  align-items: center;
}

.text-content {
  padding: 40px 24px 40px 40px;
}

.text-content h1 {
  font-size: 56px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 24px;
  color: hsl(234, 29%, 20%);
}

.text-content p {
  font-size: 16px;
  margin-bottom: 24px;
  color: hsl(234, 29%, 20%);
}

/* Benefits List */
.benefits-list {
  list-style: none;
  margin-bottom: 40px;
}

.benefits-list li {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 10px;
  font-size: 16px;
  line-height: 1.5;
}

.list-icon {
  width: 21px;
  height: 21px;
  flex-shrink: 0;
  margin-top: 2px;
}

/* Form Styles */
.signup-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

label {
  font-size: 12px;
  font-weight: 700;
  color: hsl(234, 29%, 20%);
}

.error-message {
  font-size: 12px;
  font-weight: 700;
  color: hsl(4, 100%, 67%);
  display: none;
}

.error-message.show {
  display: block;
}

.email-input {
  width: 100%;
  padding: 18px 24px;
  border: 1px solid hsl(0, 0%, 87%);
  border-radius: 8px;
  font-size: 16px;
  font-family: 'Roboto', sans-serif;
  color: hsl(234, 29%, 20%);
  background-color: hsl(0, 0%, 100%);
  transition: all 0.2s ease;
}

.email-input:focus {
  outline: none;
  border-color: hsl(234, 29%, 20%);
}

.email-input.error {
  border-color: hsl(4, 100%, 67%);
  background-color: hsl(4, 100%, 95%);
  color: hsl(4, 100%, 67%);
}

.email-input::placeholder {
  color: hsl(0, 0%, 58%);
}

.subscribe-btn {
  width: 100%;
  padding: 18px 24px;
  background-color: hsl(234, 29%, 20%);
  color: hsl(0, 0%, 100%);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Roboto', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
}

.subscribe-btn:hover {
  background: linear-gradient(135deg, hsl(13, 100%, 72%), hsl(353, 100%, 62%));
  box-shadow: 0 16px 32px rgba(255, 97, 85, 0.5);
}

/* Image Content */
.image-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.desktop-image {
  width: 100%;
  height: auto;
  display: block;
}

.mobile-image {
  display: none;
}

/* Success Card */
.success-card {
  background-color: hsl(0, 0%, 100%);
  border-radius: 36px;
  padding: 48px;
  width: 100%;
  max-width: 504px;
  box-shadow: 0 15px 60px rgba(0, 0, 0, 0.25);
}

.success-content {
  text-align: left;
}

.success-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 40px;
}

.success-card h1 {
  font-size: 56px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 24px;
  color: hsl(234, 29%, 20%);
}

.success-card p {
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 40px;
  color: hsl(234, 29%, 20%);
}

.dismiss-btn {
  width: 100%;
  padding: 18px 24px;
  background-color: hsl(234, 29%, 20%);
  color: hsl(0, 0%, 100%);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Roboto', sans-serif;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dismiss-btn:hover {
  background: linear-gradient(135deg, hsl(13, 100%, 72%), hsl(353, 100%, 62%));
  box-shadow: 0 16px 32px rgba(255, 97, 85, 0.5);
}

/* Hidden class */
.hidden {
  display: none !important;
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  margin-top: 2rem;
  color: hsl(0, 0%, 100%);
}

.attribution a {
  color: hsl(228, 45%, 44%);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  body {
    background-color: hsl(0, 0%, 100%);
    padding: 0;
    justify-content: flex-start;
  }

  .container {
    min-height: 100vh;
    max-width: none;
  }

  .signup-card {
    border-radius: 0;
    padding: 0;
    box-shadow: none;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .content {
    grid-template-columns: 1fr;
    gap: 0;
    flex-direction: column-reverse;
    display: flex;
    min-height: 100vh;
  }

  .image-content {
    order: -1;
    margin-bottom: 0;
  }

  .desktop-image {
    display: none;
  }

  .mobile-image {
    display: block;
    width: 100%;
    height: auto;
  }

  .text-content {
    padding: 40px 24px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .text-content h1 {
    font-size: 40px;
    margin-bottom: 16px;
  }

  .text-content p {
    margin-bottom: 20px;
  }

  .benefits-list {
    margin-bottom: 32px;
  }

  .success-card {
    border-radius: 0;
    padding: 24px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    box-shadow: none;
    max-width: none;
  }

  .success-content {
    width: 100%;
  }

  .success-card h1 {
    font-size: 40px;
    margin-bottom: 16px;
  }

  .success-card p {
    margin-bottom: 32px;
  }

  .attribution {
    position: fixed;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    z-index: 1000;
  }
}

/* Small mobile adjustments */
@media (max-width: 375px) {
  .text-content {
    padding: 24px 20px;
  }

  .success-card {
    padding: 20px;
  }

  .text-content h1,
  .success-card h1 {
    font-size: 36px;
  }
}
