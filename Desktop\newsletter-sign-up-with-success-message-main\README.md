# Frontend Mentor - Newsletter sign-up form with success message solution

This is a solution to the [Newsletter sign-up form with success message challenge on Frontend Mentor](https://www.frontendmentor.io/challenges/newsletter-signup-form-with-success-message-3FC1AZbNrv). Frontend Mentor challenges help you improve your coding skills by building realistic projects.

## Table of contents

- [Overview](#overview)
  - [The challenge](#the-challenge)
  - [Screenshot](#screenshot)
- [My process](#my-process)
  - [Built with](#built-with)
  - [What I learned](#what-i-learned)
  - [Continued development](#continued-development)
- [Author](#author)

## Overview

### The challenge

Users should be able to:

- Add their email and submit the form
- See a success message with their email after successfully submitting the form
- See form validation messages if:
  - The field is left empty
  - The email address is not formatted correctly
- View the optimal layout for the interface depending on their device's screen size
- See hover and focus states for all interactive elements on the page

![Design preview for the Newsletter sign-up form with success message coding challenge](./preview.jpg)


## My process

### Built with

- Semantic HTML5 markup
- CSS custom properties
- CSS Grid
- Flexbox
- Mobile-first workflow
- Vanilla JavaScript
- Local font files (Roboto)
- Form validation with regex
- Responsive design

### What I learned

This project helped me practice several key frontend development concepts:

**Form Validation**: Implemented comprehensive client-side validation with real-time feedback:

```js
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

function showError(message) {
    errorMessage.textContent = message;
    errorMessage.classList.add('show');
    emailInput.classList.add('error');
}
```

**Responsive Design**: Created a layout that adapts seamlessly between desktop and mobile:

```css
@media (max-width: 768px) {
  .content {
    grid-template-columns: 1fr;
    flex-direction: column-reverse;
    display: flex;
  }

  .desktop-image { display: none; }
  .mobile-image { display: block; }
}
```

**State Management**: Handled view transitions between sign-up form and success message:

```js
function showSuccessMessage(email) {
    confirmedEmail.textContent = email;
    signupCard.classList.add('hidden');
    successCard.classList.remove('hidden');
}
```

**Accessibility**: Ensured proper keyboard navigation and screen reader support with semantic HTML and ARIA attributes.

### Continued development

Areas I want to continue focusing on in future projects:

- **Advanced CSS animations** - Adding smooth transitions between states
- **Progressive enhancement** - Ensuring the form works without JavaScript
- **Advanced form validation** - Implementing server-side validation patterns
- **Performance optimization** - Lazy loading images and optimizing CSS delivery

## Author

- Frontend Mentor - [@Ayokanmi Adejola](https://www.frontendmentor.io/profile/Ayokanmi-Adejola)
